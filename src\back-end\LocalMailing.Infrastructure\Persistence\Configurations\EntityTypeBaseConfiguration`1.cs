using Fly.Framework.Core.Repositories.Persistence;
using LocalMailing.Domain.Entities.SystemBased;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LocalMailing.Infrastructure.Persistence.Configurations;

public class UserConfiguration : EntityTypeBaseConfiguration<TUser>
{
    public override void Configure(EntityTypeBuilder<TUser> builder)
    {
        base.Configure(builder);

        builder.ToTable("users");

        builder.Property(u => u.Username)
            .HasColumnName("username")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(u => u.Phone)
            .HasColumnName("phone")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(u => u.Email)
            .HasColumnName("email")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(u => u.PasswordHash)
            .HasColumnName("password_hash")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(u => u.FullName)
            .HasColumnName("full_name")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(u => u.DepartmentId)
            .HasColumnName("department_id")
            .IsRequired();

        // Relationships
        builder.HasOne(u => u.Department)
            .WithMany(d => d.Users)
            .HasForeignKey(u => u.DepartmentId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(u => u.UserRoles)
            .WithOne(ur => ur.User)
            .HasForeignKey(ur => ur.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(u => u.MessageAccesses)
            .WithOne(ma => ma.User)
            .HasForeignKey(ma => ma.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}