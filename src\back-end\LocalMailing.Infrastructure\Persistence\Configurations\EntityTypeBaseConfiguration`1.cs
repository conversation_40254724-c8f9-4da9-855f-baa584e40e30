using Fly.Framework.Core.Repositories.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Entities.SystemBased;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LocalMailing.Infrastructure.Persistence.Configurations;

public class UserConfiguration : EntityTypeBaseConfiguration<TUser>
{
    public override void Configure(EntityTypeBuilder<TUser> builder)
    {
        base.Configure(builder);

    }
}