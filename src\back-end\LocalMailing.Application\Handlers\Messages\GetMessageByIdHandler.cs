using LocalMailing.Application.DTOs;
using LocalMailing.Application.Queries.Messages;
using LocalMailing.Application.Repositories;
using LocalMailing.Domain.Exceptions;
using Mapster;
using MediatR;

namespace LocalMailing.Application.Handlers.Messages
{
    public class GetMessageByIdHandler : IRequestHandler<GetMessageByIdQuery, MessageDto?>
    {
        private readonly IMessageRepository _messageRepository;

        public GetMessageByIdHandler(IMessageRepository messageRepository)
        {
            _messageRepository = messageRepository;
        }

        public async Task<MessageDto?> Handle(GetMessageByIdQuery request, CancellationToken cancellationToken)
        {
            var message = await _messageRepository.GetWithDetailsAsync(request.Id, cancellationToken);
            
            if (message == null)
                return null;

            // TODO: Check user access permissions
            // if (!await _authorizationService.CanAccessMessage(request.UserId, message.Id))
            //     throw new UnauthorizedMessageAccessException(request.UserId, message.Id);

            return message.Adapt<MessageDto>();
        }
    }
}
