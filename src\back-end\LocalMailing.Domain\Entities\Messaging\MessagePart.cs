using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Entities.SystemBased;
using LocalMailing.Domain.Enums;

namespace LocalMailing.Domain.Entities.Messaging
{
    public class MessagePart : EntityBase
    {
        public Guid MessageId { get; set; }
        public Guid DepartmentId { get; set; }
        public ParticipantRole Role { get; set; }
        public ParticipantType Type { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; } = null!;
        public virtual Department Department { get; set; } = null!;
    }
}
