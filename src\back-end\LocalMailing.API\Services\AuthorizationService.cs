using LocalMailing.Application.Interfaces;
using LocalMailing.Application.Repositories;

namespace LocalMailing.API.Services
{
    public class AuthorizationService : IAuthorizationService
    {
        private readonly IUserRepository _userRepository;
        private readonly IMessageRepository _messageRepository;

        public AuthorizationService(IUserRepository userRepository, IMessageRepository messageRepository)
        {
            _userRepository = userRepository;
            _messageRepository = messageRepository;
        }

        public async Task<bool> CanAccessMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default)
        {
            var message = await _messageRepository.GetWithDetailsAsync(messageId, cancellationToken);
            if (message == null) return false;

            // User can access if they created the message
            if (message.CreatedBy == userId) return true;

            // User can access if they have explicit access
            if (message.MessageAccesses.Any(ma => ma.UserId == userId)) return true;

            // User can access if their department is a participant
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            if (user != null && message.MessageParts.Any(mp => mp.DepartmentId == user.DepartmentId))
                return true;

            return false;
        }

        public async Task<bool> CanCreateMessageAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            // For now, all authenticated users can create messages
            // This can be enhanced with role-based permissions
            var user = await _userRepository.GetByIdAsync(userId, cancellationToken);
            return user != null;
        }

        public async Task<bool> CanUpdateMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default)
        {
            var message = await _messageRepository.GetByIdAsync(messageId, cancellationToken);
            if (message == null) return false;

            // Only the creator can update the message (for now)
            return message.CreatedBy == userId;
        }

        public async Task<bool> CanDeleteMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default)
        {
            var message = await _messageRepository.GetByIdAsync(messageId, cancellationToken);
            if (message == null) return false;

            // Only the creator can delete the message (for now)
            return message.CreatedBy == userId;
        }

        public async Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default)
        {
            // TODO: Implement role-based permission checking
            // This would involve checking user roles and their associated permissions
            var user = await _userRepository.GetWithDepartmentAsync(userId, cancellationToken);
            return user != null;
        }
    }
}
