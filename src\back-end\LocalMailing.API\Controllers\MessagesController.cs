using LocalMailing.Application.Commands.Messages;
using LocalMailing.Application.DTOs;
using LocalMailing.Application.Queries.Messages;
using LocalMailing.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace LocalMailing.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MessagesController : ControllerBase
    {
        private readonly IMediator _mediator;

        public MessagesController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        public async Task<ActionResult<List<MessageDto>>> GetMessages(
            [FromQuery] Guid? departmentId = null,
            [FromQuery] WorkflowState? workflowState = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var userId = GetCurrentUserId();
            
            var query = new GetMessagesQuery(userId)
            {
                DepartmentId = departmentId,
                WorkflowState = workflowState,
                SearchTerm = searchTerm,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var messages = await _mediator.Send(query);
            return Ok(messages);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<MessageDto>> GetMessage(Guid id)
        {
            var userId = GetCurrentUserId();
            var query = new GetMessageByIdQuery(id, userId);
            
            var message = await _mediator.Send(query);
            
            if (message == null)
                return NotFound();

            return Ok(message);
        }

        [HttpPost]
        public async Task<ActionResult<MessageDto>> CreateMessage([FromBody] CreateMessageDto dto)
        {
            var userId = GetCurrentUserId();
            
            var command = new CreateMessageCommand
            {
                Type = dto.Type,
                Title = dto.Title,
                Subject = dto.Subject,
                Content = dto.Content,
                Mode = dto.Mode,
                Direction = dto.Direction,
                Priority = dto.Priority,
                ConfidentialityLevel = dto.ConfidentialityLevel,
                DueDate = dto.DueDate,
                RecipientDepartmentIds = dto.RecipientDepartmentIds,
                CreatedBy = userId
            };

            var message = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetMessage), new { id = message.Id }, message);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<MessageDto>> UpdateMessage(Guid id, [FromBody] UpdateMessageDto dto)
        {
            var userId = GetCurrentUserId();
            
            var command = new UpdateMessageCommand
            {
                Id = id,
                Title = dto.Title,
                Subject = dto.Subject,
                Content = dto.Content,
                Priority = dto.Priority,
                DueDate = dto.DueDate,
                UpdatedBy = userId
            };

            var message = await _mediator.Send(command);
            return Ok(message);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMessage(Guid id)
        {
            var userId = GetCurrentUserId();
            
            var command = new DeleteMessageCommand
            {
                Id = id,
                DeletedBy = userId
            };

            var result = await _mediator.Send(command);
            
            if (!result)
                return NotFound();

            return NoContent();
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                throw new UnauthorizedAccessException("Invalid user token");

            return userId;
        }
    }
}
