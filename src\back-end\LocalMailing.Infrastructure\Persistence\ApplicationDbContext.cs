using Microsoft.EntityFrameworkCore;
using LocalMailing.Domain.Entities.SystemBased;
using LocalMailing.Domain.Entities.Messaging;
using LocalMailing.Domain.Entities.Settings;

namespace LocalMailing.Infrastructure.Persistence;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options)
{
    // System entities
    public DbSet<TUser> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }
    public DbSet<Department> Departments { get; set; }

    // Messaging entities
    public DbSet<Message> Messages { get; set; }
    public DbSet<MessageAttachment> MessageAttachments { get; set; }
    public DbSet<MessagePart> MessageParts { get; set; }
    public DbSet<MessageInfo> MessageInfos { get; set; }
    public DbSet<MessageAccess> MessageAccesses { get; set; }

    // Settings entities
    public DbSet<SystemDocumentSettings> SystemDocumentSettings { get; set; }
    public DbSet<SystemDocumentTemplate> SystemDocumentTemplates { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all entity configurations from assembly
        modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);

        // Configure indexes for performance
        modelBuilder.Entity<Message>()
            .HasIndex(m => m.WorkflowState)
            .HasDatabaseName("IX_Messages_WorkflowState");

        modelBuilder.Entity<Message>()
            .HasIndex(m => m.CreatedAtUtc)
            .HasDatabaseName("IX_Messages_CreatedAtUtc");

        modelBuilder.Entity<MessageAccess>()
            .HasIndex(ma => new { ma.MessageId, ma.UserId })
            .HasDatabaseName("IX_MessageAccess_MessageId_UserId");

        modelBuilder.Entity<MessagePart>()
            .HasIndex(mp => mp.DepartmentId)
            .HasDatabaseName("IX_MessageParts_DepartmentId");

        modelBuilder.Entity<TUser>()
            .HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        modelBuilder.Entity<TUser>()
            .HasIndex(u => u.Email)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Dispatch domain events before saving
        // var entities = ChangeTracker.Entries<AggregateRoot>()
        //     .Where(e => e.Entity.DomainEvents.Any())
        //     .Select(e => e.Entity)
        //     .ToList();

        // var domainEvents = entities
        //     .SelectMany(e => e.DomainEvents)
        //     .ToList();

        // entities.ForEach(e => e.ClearDomainEvents());

        // Save changes first
        var result = base.SaveChangesAsync(cancellationToken);

        // TODO: Dispatch domain events through mediator
        // This would typically be done through a domain event dispatcher

        return result;
    }
}
