using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace LocalMailing.Infrastructure.Persistence;



public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options)
{
    // public DbSet<MailItem> MailItems { get; set; }
    // public DbSet<Participant> Participants { get; set; }
    // public DbSet<Attachment> Attachments { get; set; }
    // public DbSet<Note> Notes { get; set; }
    // public DbSet<Route> Routes { get; set; }
    // public DbSet<AccessGrant> AccessGrants { get; set; }
    // public DbSet<MailItemTag> MailItemTags { get; set; }
    // public DbSet<ExternalParty> ExternalParties { get; set; }
    // public DbSet<RetentionClass> RetentionClasses { get; set; }
    // public DbSet<AuditLog> AuditLogs { get; set; }
    // public DbSet<Department> Departments { get; set; }
    // public DbSet<User> Users { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfigurationsFromAssembly(GetType().Assembly);

        // Apply entity configurations
        // modelBuilder.ApplyConfiguration(new MailItemConfiguration());
        // modelBuilder.ApplyConfiguration(new ParticipantConfiguration());
        // modelBuilder.ApplyConfiguration(new AttachmentConfiguration());
        // modelBuilder.ApplyConfiguration(new NoteConfiguration());
        // modelBuilder.ApplyConfiguration(new RouteConfiguration());
        // modelBuilder.ApplyConfiguration(new AccessGrantConfiguration());
        // modelBuilder.ApplyConfiguration(new MailItemTagConfiguration());
        // modelBuilder.ApplyConfiguration(new ExternalPartyConfiguration());
        // modelBuilder.ApplyConfiguration(new RetentionClassConfiguration());
        // modelBuilder.ApplyConfiguration(new AuditLogConfiguration());
        // modelBuilder.ApplyConfiguration(new DepartmentConfiguration());
        // modelBuilder.ApplyConfiguration(new UserConfiguration());

        // Configure value objects
        // modelBuilder.Entity<MailItem>()
        //     .OwnsOne(m => m.ReferenceNumber, rn =>
        //     {
        //         rn.Property(r => r.Value).HasColumnName("ReferenceNumber");
        //     });
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Dispatch domain events before saving
        // var entities = ChangeTracker.Entries<AggregateRoot>()
        //     .Where(e => e.Entity.DomainEvents.Any())
        //     .Select(e => e.Entity)
        //     .ToList();

        // var domainEvents = entities
        //     .SelectMany(e => e.DomainEvents)
        //     .ToList();

        // entities.ForEach(e => e.ClearDomainEvents());

        // Save changes first
        var result = base.SaveChangesAsync(cancellationToken);

        // TODO: Dispatch domain events through mediator
        // This would typically be done through a domain event dispatcher

        return result;
    }
}
