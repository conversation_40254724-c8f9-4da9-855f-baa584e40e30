using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Enums;

namespace LocalMailing.Domain.Events
{
    public class MessageStatusChangedEvent : BaseDomainEvent
    {
        public Guid MessageId { get; }
        public WorkflowState OldStatus { get; }
        public WorkflowState NewStatus { get; }
        public Guid ChangedBy { get; }

        public MessageStatusChangedEvent(Guid messageId, WorkflowState oldStatus, WorkflowState newStatus, Guid changedBy)
        {
            MessageId = messageId;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            ChangedBy = changedBy;
        }
    }
}
