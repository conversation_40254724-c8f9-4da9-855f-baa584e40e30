using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LocalMailing.Domain.Events;

public class NoteAddedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid NoteId { get; }
    public Guid AuthorUserId { get; }

    public NoteAddedEvent(Guid mailItemId, Guid noteId, Guid authorUserId)
    {
        MailItemId = mailItemId;
        NoteId = noteId;
        AuthorUserId = authorUserId;
    }
}
