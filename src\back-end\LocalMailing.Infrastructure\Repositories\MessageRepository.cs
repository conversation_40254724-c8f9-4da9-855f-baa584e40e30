using LocalMailing.Application.Repositories;
using LocalMailing.Domain.Entities.Messaging;
using LocalMailing.Domain.Enums;
using LocalMailing.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace LocalMailing.Infrastructure.Repositories
{
    public class MessageRepository : Repository<Message>, IMessageRepository
    {
        public MessageRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<Message?> GetWithDetailsAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(m => m.Attachments)
                .Include(m => m.MessageParts)
                    .ThenInclude(mp => mp.Department)
                .Include(m => m.MessageInfo)
                .Include(m => m.MessageAccesses)
                    .ThenInclude(ma => ma.User)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted, cancellationToken);
        }

        public async Task<List<Message>> GetByDepartmentAsync(Guid departmentId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(m => m.MessageParts)
                .Where(m => !m.IsDeleted && m.MessageParts.Any(mp => mp.DepartmentId == departmentId))
                .OrderByDescending(m => m.CreatedAtUtc)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<Message>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(m => m.MessageAccesses)
                .Where(m => !m.IsDeleted && 
                           (m.CreatedBy == userId || m.MessageAccesses.Any(ma => ma.UserId == userId)))
                .OrderByDescending(m => m.CreatedAtUtc)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<Message>> GetByWorkflowStateAsync(WorkflowState workflowState, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(m => !m.IsDeleted && m.WorkflowState == workflowState)
                .OrderByDescending(m => m.CreatedAtUtc)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<Message>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(m => !m.IsDeleted && 
                           (m.Title.Contains(searchTerm) || 
                            m.Subject.Contains(searchTerm) || 
                            m.Content.Contains(searchTerm)))
                .OrderByDescending(m => m.CreatedAtUtc)
                .ToListAsync(cancellationToken);
        }
    }
}
