version: '3.9'

services:
  # MySQL Database for development
  db:
    image: mysql:8.0
    container_name: localmailing-db-dev
    restart: always
    environment:
      MYSQL_DATABASE: localmailing
      MYSQL_USER: localmailing
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: rootpassword
    ports:
      - "3306:3306"
    volumes:
      - db_data_dev:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - localmailing-network-dev
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: localmailing-phpmyadmin-dev
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: localmailing
      PMA_PASSWORD: password
    depends_on:
      - db
    networks:
      - localmailing-network-dev

volumes:
  db_data_dev:
    driver: local

networks:
  localmailing-network-dev:
    driver: bridge
