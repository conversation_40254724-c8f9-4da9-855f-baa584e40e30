
---

# 📌 Local-Mailing System

## 📖 Overview

The **Local-Mailing System** is an enterprise-level internal communication and document management solution. It enables **secure message creation, routing, tracking, and archiving** across departments with **role-based access control**.

The system is built on **Clean Architecture principles**, ensuring **separation of concerns, testability, and scalability**.

---

## 🏛️ Architecture Layers

### 🔹 **Core Layer**

* Contains **domain entities** and **business rules**.
* Pure C# objects (POCOs) with no dependency on infrastructure.
* Includes:

  * Entities (User, Role, Message, etc.)
  * Value Objects
  * Domain Events

### 🔹 **Application Layer**

* Implements **CQRS + Mediator Pattern** using **MediatR**.
* Orchestrates business logic between Core and Infrastructure.
* Includes:

  * Commands & Queries (e.g., `CreateMessageCommand`, `GetUserListQuery`)
  * DTOs (using **Mapster** for mapping)
  * Interfaces (repositories, services)
  * Validation (FluentValidation)

### 🔹 **Infrastructure Layer**

* Implements persistence & external services.
* Includes:

  * **EF Core** DbContext & Repositories
  * **Entity Configurations** (fluent API for each entity)
  * **Design-Time EF Config** for migrations (`IDesignTimeDbContextFactory`)
  * Logging (Serilog)
  * Email/Notification integrations

### 🔹 **API Layer**

* ASP.NET Core Web API exposing endpoints.
* Features:

  * JWT Authentication & Authorization
  * Controllers calling Application layer (via MediatR)
  * Swagger (OpenAPI) for documentation
  * Global Exception Handling Middleware
  * Response Wrappers (standard API response format)

---

## 🗄️ Entities (Core Layer)

### 🔑 Security & Access

* **User** (username, phone, email, passwordHash, fullname, departmentId)
* **Role** (name)
* **RolePermission** (roleId, permissionId)
* **Permission** (key, name, description)
* **Department** (name, parentId)

### ⚙️ System Settings

* **SystemDocumentSettings** (defaultPageSize, fonts)
* **SystemDocumentTemplate** (backgroundImage)

### ✉️ Messaging

* **Message** (type, title, subject, mode)
* **MessageAtt** (messageId, file)
* **MessagePart** (messageId, departmentId)
* **MessageInfo** (messageId, serial, fileNumber)
* **MessageAccess** (messageId, userId, date, status)

---

## 🔄 Workflow (CQRS + Mediator)

* **Command Example:**

  * `CreateMessageCommand` → Validated by `CreateMessageValidator` → Handled by `CreateMessageHandler` → Persists via `IMessageRepository`.

* **Query Example:**

  * `GetMessageByIdQuery` → Handled by `GetMessageByIdHandler` → Uses repository → Returns DTO mapped via **Mapster**.

---

## 🛠️ Technical Stack

* **Backend:** ASP.NET Core 8 (Web API)
* **Frontend:** React.js (separate repo)
* **Database:** MySQL (EF Core provider)
* **Mapping:** Mapster (DTO ↔ Entity)
* **Mediator:** MediatR
* **Validation:** FluentValidation
* **Logging:** Serilog
* **Auth:** JWT + Role/Permission-based access
* **Containerization:** Docker + Docker Compose

---

## 🐳 Deployment with Docker

**docker-compose.yml example:**

```yaml
version: '3.9'
services:
  api:
    build: ./src/API
    ports:
      - "5000:80"
    depends_on:
      - db
    environment:
      - ConnectionStrings__DefaultConnection=server=db;database=localmailing;user=root;password=******;

  db:
    image: mysql:8
    restart: always
    environment:
      MYSQL_DATABASE: localmailing
      MYSQL_ROOT_PASSWORD: ******
    volumes:
      - db_data:/var/lib/mysql

  frontend:
    build: ./src/Frontend
    ports:
      - "3000:80"
    depends_on:
      - api

volumes:
  db_data:
```

---

## 📂 Suggested Folder Structure

```
src/
 ├── Core/
 │    ├── Entities/
 │    ├── ValueObjects/
 │    ├── Events/
 │    └── Exceptions/
 │
 ├── Application/
 │    ├── Commands/
 │    │     └── Messages/
 │    ├── Queries/
 │    │     └── Messages/
 │    ├── Interfaces/
 │    ├── DTOs/
 │    ├── Validators/
 │    └── Mappings/ (Mapster configs)
 │
 ├── Infrastructure/
 │    ├── Persistence/
 │    │     ├── LocalMailingDbContext.cs
 │    │     ├── Configurations/ (EntityTypeConfiguration classes)
 │    │     └── DesignTimeDbContextFactory.cs
 │    ├── Repositories/
 │    ├── Logging/
 │    └── Services/
 │
 ├── API/
 │    ├── Controllers/
 │    ├── Middleware/
 │    ├── Program.cs
 │    └── appsettings.json
 │
 └── Frontend/ (React project)
```

---

## 🚀 Development Notes for AI Agent

1. Use **CQRS with MediatR** for all business operations.
2. Use **Repositories** in Infrastructure to abstract EF Core DbContext.
3. Apply **EntityTypeConfiguration** for each entity instead of attributes.
4. Use **Mapster** for DTO ↔ Entity conversions.
5. Implement **IDesignTimeDbContextFactory** for EF Core migrations.
6. Ensure **unit tests** for commands, queries, and repositories.
7. Provide **Swagger/OpenAPI** documentation.
8. Optimize DB with indexes for `MessageId`, `UserId`, `DepartmentId`.
9. Secure APIs with **JWT + Role/Permission-based filters**.

---

👉 Do you want me to also **add example code snippets** (like `CreateMessageCommandHandler`, `EntityConfiguration`, or `DesignTimeDbContextFactory`) to make the README a practical dev guide?
