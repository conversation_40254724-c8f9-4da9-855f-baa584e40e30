using LocalMailing.Domain.Enums;

namespace LocalMailing.Application.DTOs
{
    public class MessageDto
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public MailMode Mode { get; set; }
        public MailDirection Direction { get; set; }
        public Priority Priority { get; set; }
        public ConfidentialityLevel ConfidentialityLevel { get; set; }
        public WorkflowState WorkflowState { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime CreatedAtUtc { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public List<MessageAttachmentDto> Attachments { get; set; } = new();
        public MessageInfoDto? MessageInfo { get; set; }
    }

    public class CreateMessageDto
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public MailMode Mode { get; set; }
        public MailDirection Direction { get; set; }
        public Priority Priority { get; set; }
        public ConfidentialityLevel ConfidentialityLevel { get; set; }
        public DateTime? DueDate { get; set; }
        public List<Guid> RecipientDepartmentIds { get; set; } = new();
    }

    public class UpdateMessageDto
    {
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Priority Priority { get; set; }
        public DateTime? DueDate { get; set; }
    }

    public class MessageAttachmentDto
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
    }

    public class MessageInfoDto
    {
        public string Serial { get; set; } = string.Empty;
        public string FileNumber { get; set; } = string.Empty;
        public string? ReferenceNumber { get; set; }
        public DateTime? RegistrationDate { get; set; }
    }
}
