using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Entities.Messaging;

namespace LocalMailing.Domain.Entities.SystemBased
{
    public class TUser : EntityBase
    {
        public string Username { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public Guid DepartmentId { get; set; }

        // Navigation properties
        public virtual Department Department { get; set; } = null!;
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<MessageAccess> MessageAccesses { get; set; } = new List<MessageAccess>();
    }
}