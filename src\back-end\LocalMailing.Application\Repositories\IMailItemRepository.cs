using LocalMailing.Domain.Entities.Messaging;
using LocalMailing.Domain.Enums;

namespace LocalMailing.Application.Repositories
{
    public interface IMessageRepository : IRepository<Message>
    {
        Task<Message?> GetWithDetailsAsync(Guid id, CancellationToken cancellationToken = default);
        Task<List<Message>> GetByDepartmentAsync(Guid departmentId, CancellationToken cancellationToken = default);
        Task<List<Message>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default);
        Task<List<Message>> GetByWorkflowStateAsync(WorkflowState workflowState, CancellationToken cancellationToken = default);
        Task<List<Message>> SearchAsync(string searchTerm, CancellationToken cancellationToken = default);
    }
}
