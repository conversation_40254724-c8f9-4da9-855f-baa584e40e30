using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace  LocalMailing.Application.Repositories;

public interface IMailItemRepository : IRepository<MailItem>
{
    Task<MailItem?> GetDetailsAsync(Guid id, CancellationToken cancellationToken = default);
    Task<SearchResult<MailItem>> SearchAsync(SearchSpec spec, CancellationToken cancellationToken = default);
    Task<List<MailItem>> GetByDepartmentAsync(Guid departmentId, CancellationToken cancellationToken = default);
    Task<List<MailItem>> GetByUserAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<List<MailItem>> GetByWorkflowStateAsync(string workflowState, CancellationToken cancellationToken = default);
}
