using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LocalMailing.Infrastructure.Persistence;

public class DebugDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        // var connectionString = "Host=localhost;Database=localmailing;Username=postgres;Password=password";
        var connectionString = "Server=localhost;Database=localmailing;User=localmailing;Password=password;";

        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();

        // optionsBuilder.UseNpgsql(connectionString);
        optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

        return new ApplicationDbContext(optionsBuilder.Options);
    }
}