using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace LocalMailing.Infrastructure.Persistence;

public class DebugDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
{
    public ApplicationDbContext CreateDbContext(string[] args)
    {
        // var connectionString = "Host=localhost;Database=localmailing;Username=postgres;Password=password";
        var connectionString = "Server=localhost;Database=localmailing;User=localmailing;Password=password;";

        var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();

        // optionsBuilder.UseNpgsql(connectionString);
        optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));

        return new ApplicationDbContext(optionsBuilder.Options);
    }
}