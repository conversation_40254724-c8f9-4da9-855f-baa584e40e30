# Use the official .NET 9 runtime as a parent image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Use the official .NET 9 SDK for building
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY ["LocalMailing.API/LocalMailing.API.csproj", "LocalMailing.API/"]
COPY ["LocalMailing.Application/LocalMailing.Application.csproj", "LocalMailing.Application/"]
COPY ["LocalMailing.Infrastructure/LocalMailing.Infrastructure.csproj", "LocalMailing.Infrastructure/"]
COPY ["LocalMailing.Domain/LocalMailing.Domain.csproj", "LocalMailing.Domain/"]
COPY ["Fly.Framework/Fly.Framework.Core/Fly.Framework.Core.csproj", "Fly.Framework/Fly.Framework.Core/"]
COPY ["Fly.Framework/Fly.Framework.Domain/Fly.Framework.Domain.csproj", "Fly.Framework/Fly.Framework.Domain/"]

# Restore dependencies
RUN dotnet restore "LocalMailing.API/LocalMailing.API.csproj"

# Copy the rest of the source code
COPY . .

# Build the application
WORKDIR "/src/LocalMailing.API"
RUN dotnet build "LocalMailing.API.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "LocalMailing.API.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

ENTRYPOINT ["dotnet", "LocalMailing.API.dll"]
