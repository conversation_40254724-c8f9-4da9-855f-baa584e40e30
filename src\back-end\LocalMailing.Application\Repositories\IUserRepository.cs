using LocalMailing.Domain.Entities.SystemBased;

namespace LocalMailing.Application.Repositories
{
    public interface IUserRepository : IRepository<TUser>
    {
        Task<TUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
        Task<TUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
        Task<TUser?> GetWithDepartmentAsync(Guid id, CancellationToken cancellationToken = default);
        Task<List<TUser>> GetByDepartmentAsync(Guid departmentId, CancellationToken cancellationToken = default);
        Task<bool> UsernameExistsAsync(string username, CancellationToken cancellationToken = default);
        Task<bool> EmailExistsAsync(string email, CancellationToken cancellationToken = default);
    }
}
