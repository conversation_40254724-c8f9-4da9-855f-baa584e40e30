using Fly.Framework.Core.Repositories.Persistence;
using LocalMailing.Domain.Entities.SystemBased;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LocalMailing.Infrastructure.Persistence.Configurations
{
    public class RoleConfiguration : EntityTypeBaseConfiguration<Role>
    {
        public override void Configure(EntityTypeBuilder<Role> builder)
        {
            base.Configure(builder);

            builder.ToTable("roles");

            builder.Property(r => r.Name)
                .HasColumnName("name")
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(r => r.Description)
                .HasColumnName("description")
                .HasMaxLength(500)
                .IsRequired();

            // Relationships
            builder.HasMany(r => r.UserRoles)
                .WithOne(ur => ur.Role)
                .HasForeignKey(ur => ur.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(r => r.RolePermissions)
                .WithOne(rp => rp.Role)
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }

    public class PermissionConfiguration : EntityTypeBaseConfiguration<Permission>
    {
        public override void Configure(EntityTypeBuilder<Permission> builder)
        {
            base.Configure(builder);

            builder.ToTable("permissions");

            builder.Property(p => p.Key)
                .HasColumnName("key")
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(p => p.Name)
                .HasColumnName("name")
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(p => p.Description)
                .HasColumnName("description")
                .HasMaxLength(500)
                .IsRequired();

            builder.HasIndex(p => p.Key)
                .IsUnique()
                .HasDatabaseName("IX_Permissions_Key");

            // Relationships
            builder.HasMany(p => p.RolePermissions)
                .WithOne(rp => rp.Permission)
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }

    public class UserRoleConfiguration : EntityTypeBaseConfiguration<UserRole>
    {
        public override void Configure(EntityTypeBuilder<UserRole> builder)
        {
            base.Configure(builder);

            builder.ToTable("user_roles");

            builder.Property(ur => ur.UserId)
                .HasColumnName("user_id")
                .IsRequired();

            builder.Property(ur => ur.RoleId)
                .HasColumnName("role_id")
                .IsRequired();

            builder.HasIndex(ur => new { ur.UserId, ur.RoleId })
                .IsUnique()
                .HasDatabaseName("IX_UserRoles_UserId_RoleId");

            // Relationships configured in User and Role configurations
        }
    }

    public class RolePermissionConfiguration : EntityTypeBaseConfiguration<RolePermission>
    {
        public override void Configure(EntityTypeBuilder<RolePermission> builder)
        {
            base.Configure(builder);

            builder.ToTable("role_permissions");

            builder.Property(rp => rp.RoleId)
                .HasColumnName("role_id")
                .IsRequired();

            builder.Property(rp => rp.PermissionId)
                .HasColumnName("permission_id")
                .IsRequired();

            builder.HasIndex(rp => new { rp.RoleId, rp.PermissionId })
                .IsUnique()
                .HasDatabaseName("IX_RolePermissions_RoleId_PermissionId");

            // Relationships configured in Role and Permission configurations
        }
    }
}
