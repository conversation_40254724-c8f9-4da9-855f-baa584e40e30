using Fly.Framework.Core.Repositories.Persistence;
using LocalMailing.Domain.Entities.SystemBased;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LocalMailing.Infrastructure.Persistence.Configurations
{
    public class DepartmentConfiguration : EntityTypeBaseConfiguration<Department>
    {
        public override void Configure(EntityTypeBuilder<Department> builder)
        {
            base.Configure(builder);

            builder.ToTable("departments");

            builder.Property(d => d.Name)
                .HasColumnName("name")
                .HasMaxLength(255)
                .IsRequired();

            builder.Property(d => d.ParentId)
                .HasColumnName("parent_id")
                .IsRequired(false);

            // Self-referencing relationship
            builder.HasOne(d => d.Parent)
                .WithMany(d => d.Children)
                .HasForeignKey(d => d.ParentId)
                .OnDelete(DeleteBehavior.Restrict);

            // Relationships
            builder.HasMany(d => d.Users)
                .WithOne(u => u.Department)
                .HasForeignKey(u => u.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(d => d.MessageParts)
                .WithOne(mp => mp.Department)
                .HasForeignKey(mp => mp.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
