using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Entities.Messaging
{
    public class MessageAttachment : EntityBase
    {
        public Guid MessageId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; } = null!;
    }
}
