using LocalMailing.Application.Repositories;
using LocalMailing.Domain.Entities.SystemBased;
using LocalMailing.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace LocalMailing.Infrastructure.Repositories
{
    public class DepartmentRepository : Repository<Department>, IDepartmentRepository
    {
        public DepartmentRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<List<Department>> GetHierarchyAsync(CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(d => d.Children)
                .Include(d => d.Users)
                .Where(d => !d.IsDeleted)
                .OrderBy(d => d.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<Department>> GetChildrenAsync(Guid parentId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Where(d => d.ParentId == parentId && !d.IsDeleted)
                .OrderBy(d => d.Name)
                .ToListAsync(cancellationToken);
        }

        public async Task<Department?> GetWithUsersAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(d => d.Users)
                .Include(d => d.Children)
                .FirstOrDefaultAsync(d => d.Id == id && !d.IsDeleted, cancellationToken);
        }

        public async Task<bool> HasChildrenAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .AnyAsync(d => d.ParentId == id && !d.IsDeleted, cancellationToken);
        }
    }
}
