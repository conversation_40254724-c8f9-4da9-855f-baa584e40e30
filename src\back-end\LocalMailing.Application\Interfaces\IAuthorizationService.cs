namespace LocalMailing.Application.Interfaces
{
    public interface IAuthorizationService
    {
        Task<bool> CanAccessMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default);
        Task<bool> CanCreateMessageAsync(Guid userId, CancellationToken cancellationToken = default);
        Task<bool> CanUpdateMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default);
        Task<bool> CanDeleteMessageAsync(Guid userId, Guid messageId, CancellationToken cancellationToken = default);
        Task<bool> HasPermissionAsync(Guid userId, string permission, CancellationToken cancellationToken = default);
    }
}
