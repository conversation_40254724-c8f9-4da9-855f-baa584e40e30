using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Entities.Settings
{
    public class SystemDocumentSettings : EntityBase
    {
        public int DefaultPageSize { get; set; } = 20;
        public string Fonts { get; set; } = string.Empty;
        public string? LogoPath { get; set; }
        public string? HeaderText { get; set; }
        public string? FooterText { get; set; }
    }
}
