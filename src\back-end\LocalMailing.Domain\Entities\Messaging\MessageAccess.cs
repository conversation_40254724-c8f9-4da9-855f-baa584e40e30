using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Entities.SystemBased;

namespace LocalMailing.Domain.Entities.Messaging
{
    public class MessageAccess : EntityBase
    {
        public Guid MessageId { get; set; }
        public Guid UserId { get; set; }
        public DateTime AccessDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? Action { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; } = null!;
        public virtual TUser User { get; set; } = null!;
    }
}
