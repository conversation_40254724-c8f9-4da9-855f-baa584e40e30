using FluentAssertions;
using LocalMailing.Domain.Entities.Messaging;
using LocalMailing.Domain.Enums;
using Xunit;

namespace LocalMailing.Tests.Domain.Entities
{
    public class MessageTests
    {
        [Fact]
        public void Message_ShouldBeCreated_WithValidProperties()
        {
            // Arrange
            var messageId = Guid.NewGuid();
            var createdBy = Guid.NewGuid();
            var title = "Test Message";
            var subject = "Test Subject";
            var content = "Test Content";

            // Act
            var message = new Message
            {
                Id = messageId,
                Title = title,
                Subject = subject,
                Content = content,
                Mode = MailMode.Public,
                Direction = MailDirection.Outbound,
                Priority = Priority.Normal,
                ConfidentialityLevel = ConfidentialityLevel.Internal,
                WorkflowState = WorkflowState.Draft,
                CreatedBy = createdBy,
                CreatedAtUtc = DateTime.UtcNow
            };

            // Assert
            message.Id.Should().Be(messageId);
            message.Title.Should().Be(title);
            message.Subject.Should().Be(subject);
            message.Content.Should().Be(content);
            message.Mode.Should().Be(MailMode.Public);
            message.Direction.Should().Be(MailDirection.Outbound);
            message.Priority.Should().Be(Priority.Normal);
            message.ConfidentialityLevel.Should().Be(ConfidentialityLevel.Internal);
            message.WorkflowState.Should().Be(WorkflowState.Draft);
            message.CreatedBy.Should().Be(createdBy);
            message.Attachments.Should().NotBeNull();
            message.MessageParts.Should().NotBeNull();
            message.MessageAccesses.Should().NotBeNull();
        }

        [Fact]
        public void Message_ShouldAllowAddingAttachments()
        {
            // Arrange
            var message = new Message();
            var attachment = new MessageAttachment
            {
                Id = Guid.NewGuid(),
                MessageId = message.Id,
                FileName = "test.pdf",
                FilePath = "/uploads/test.pdf",
                ContentType = "application/pdf",
                FileSize = 1024
            };

            // Act
            message.Attachments.Add(attachment);

            // Assert
            message.Attachments.Should().HaveCount(1);
            message.Attachments.First().Should().Be(attachment);
        }

        [Fact]
        public void Message_ShouldAllowAddingMessageParts()
        {
            // Arrange
            var message = new Message();
            var departmentId = Guid.NewGuid();
            var messagePart = new MessagePart
            {
                Id = Guid.NewGuid(),
                MessageId = message.Id,
                DepartmentId = departmentId,
                Role = ParticipantRole.Receiver,
                Type = ParticipantType.Department
            };

            // Act
            message.MessageParts.Add(messagePart);

            // Assert
            message.MessageParts.Should().HaveCount(1);
            message.MessageParts.First().Should().Be(messagePart);
        }
    }
}
