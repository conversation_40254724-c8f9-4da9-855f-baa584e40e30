using FluentAssertions;
using LocalMailing.Domain.ValueObjects;
using Xunit;

namespace LocalMailing.Tests.Domain.ValueObjects
{
    public class EmailAddressTests
    {
        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public void EmailAddress_ShouldBeValid_WithCorrectFormat(string email)
        {
            // Act
            var emailAddress = new EmailAddress(email);

            // Assert
            emailAddress.Value.Should().Be(email.ToLowerInvariant());
        }

        [Theory]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData("invalid-email")]
        [InlineData("@domain.com")]
        [InlineData("user@")]
        [InlineData("user.domain.com")]
        public void EmailAddress_ShouldThrowException_WithInvalidFormat(string email)
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new EmailAddress(email));
        }

        [Fact]
        public void EmailAddress_ShouldConvertToLowerCase()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var emailAddress = new EmailAddress(email);

            // Assert
            emailAddress.Value.Should().Be("<EMAIL>");
        }

        [Fact]
        public void EmailAddress_ShouldTrimWhitespace()
        {
            // Arrange
            var email = "  <EMAIL>  ";

            // Act
            var emailAddress = new EmailAddress(email);

            // Assert
            emailAddress.Value.Should().Be("<EMAIL>");
        }

        [Fact]
        public void EmailAddress_ShouldSupportImplicitConversion()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            EmailAddress emailAddress = email;
            string convertedBack = emailAddress;

            // Assert
            emailAddress.Value.Should().Be(email);
            convertedBack.Should().Be(email);
        }
    }
}
