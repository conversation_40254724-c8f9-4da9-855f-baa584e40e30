using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Enums;

namespace LocalMailing.Domain.Entities.Messaging
{
    public class Message : EntityBase
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public MailMode Mode { get; set; }
        public MailDirection Direction { get; set; }
        public Priority Priority { get; set; }
        public ConfidentialityLevel ConfidentialityLevel { get; set; }
        public WorkflowState WorkflowState { get; set; }
        public DateTime? DueDate { get; set; }

        // Navigation properties
        public virtual ICollection<MessageAttachment> Attachments { get; set; } = new List<MessageAttachment>();
        public virtual ICollection<MessagePart> MessageParts { get; set; } = new List<MessagePart>();
        public virtual MessageInfo? MessageInfo { get; set; }
        public virtual ICollection<MessageAccess> MessageAccesses { get; set; } = new List<MessageAccess>();
    }
}
