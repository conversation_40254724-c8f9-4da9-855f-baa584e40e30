using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Entities.Messaging
{
    public class MessageInfo : EntityBase
    {
        public Guid MessageId { get; set; }
        public string Serial { get; set; } = string.Empty;
        public string FileNumber { get; set; } = string.Empty;
        public string? ReferenceNumber { get; set; }
        public DateTime? RegistrationDate { get; set; }

        // Navigation properties
        public virtual Message Message { get; set; } = null!;
    }
}
