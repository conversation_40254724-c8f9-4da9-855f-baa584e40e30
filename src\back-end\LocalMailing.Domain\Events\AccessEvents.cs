using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LocalMailing.Domain.Events;

public class AccessGrantedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid GranteeId { get; }
    public string Scope { get; }

    public AccessGrantedEvent(Guid mailItemId, Guid granteeId, string scope)
    {
        MailItemId = mailItemId;
        GranteeId = granteeId;
        Scope = scope;
    }
}

public class AccessRevokedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid AccessGrantId { get; }

    public AccessRevokedEvent(Guid mailItemId, Guid accessGrantId)
    {
        MailItemId = mailItemId;
        AccessGrantId = accessGrantId;
    }
}