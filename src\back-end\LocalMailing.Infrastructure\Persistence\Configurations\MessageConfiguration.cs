using Fly.Framework.Core.Repositories.Persistence;
using LocalMailing.Domain.Entities.Messaging;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LocalMailing.Infrastructure.Persistence.Configurations
{
    public class MessageConfiguration : EntityTypeBaseConfiguration<Message>
    {
        public override void Configure(EntityTypeBuilder<Message> builder)
        {
            base.Configure(builder);

            builder.ToTable("messages");

            builder.Property(m => m.Type)
                .HasColumnName("type")
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(m => m.Title)
                .HasColumnName("title")
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(m => m.Subject)
                .HasColumnName("subject")
                .HasMaxLength(500)
                .IsRequired();

            builder.Property(m => m.Content)
                .HasColumnName("content")
                .HasColumnType("TEXT")
                .IsRequired();

            builder.Property(m => m.Mode)
                .HasColumnName("mode")
                .HasConversion<string>()
                .IsRequired();

            builder.Property(m => m.Direction)
                .HasColumnName("direction")
                .HasConversion<string>()
                .IsRequired();

            builder.Property(m => m.Priority)
                .HasColumnName("priority")
                .HasConversion<string>()
                .IsRequired();

            builder.Property(m => m.ConfidentialityLevel)
                .HasColumnName("confidentiality_level")
                .HasConversion<string>()
                .IsRequired();

            builder.Property(m => m.WorkflowState)
                .HasColumnName("workflow_state")
                .HasConversion<string>()
                .IsRequired();

            builder.Property(m => m.DueDate)
                .HasColumnName("due_date")
                .IsRequired(false);

            // Relationships
            builder.HasMany(m => m.Attachments)
                .WithOne(a => a.Message)
                .HasForeignKey(a => a.MessageId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(m => m.MessageParts)
                .WithOne(mp => mp.Message)
                .HasForeignKey(mp => mp.MessageId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(m => m.MessageInfo)
                .WithOne(mi => mi.Message)
                .HasForeignKey<MessageInfo>(mi => mi.MessageId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(m => m.MessageAccesses)
                .WithOne(ma => ma.Message)
                .HasForeignKey(ma => ma.MessageId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
