namespace LocalMailing.Domain.ValueObjects
{
    public record ReferenceNumber
    {
        public string Value { get; }

        public ReferenceNumber(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Reference number cannot be empty", nameof(value));

            Value = value.Trim().ToUpperInvariant();
        }

        public static implicit operator string(ReferenceNumber referenceNumber) => referenceNumber.Value;
        public static implicit operator ReferenceNumber(string value) => new(value);

        public override string ToString() => Value;
    }
}
