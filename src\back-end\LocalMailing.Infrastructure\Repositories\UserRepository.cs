using LocalMailing.Application.Repositories;
using LocalMailing.Domain.Entities.SystemBased;
using LocalMailing.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace LocalMailing.Infrastructure.Repositories
{
    public class UserRepository : Repository<TUser>, IUserRepository
    {
        public UserRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<TUser?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .FirstOrDefaultAsync(u => u.Username == username && !u.IsDeleted, cancellationToken);
        }

        public async Task<TUser?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .FirstOrDefaultAsync(u => u.Email == email && !u.IsDeleted, cancellationToken);
        }

        public async Task<TUser?> GetWithDepartmentAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(u => u.Department)
                .Include(u => u.UserRoles)
                    .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Id == id && !u.IsDeleted, cancellationToken);
        }

        public async Task<List<TUser>> GetByDepartmentAsync(Guid departmentId, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .Include(u => u.Department)
                .Where(u => u.DepartmentId == departmentId && !u.IsDeleted)
                .OrderBy(u => u.FullName)
                .ToListAsync(cancellationToken);
        }

        public async Task<bool> UsernameExistsAsync(string username, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .AnyAsync(u => u.Username == username && !u.IsDeleted, cancellationToken);
        }

        public async Task<bool> EmailExistsAsync(string email, CancellationToken cancellationToken = default)
        {
            return await _dbSet
                .AnyAsync(u => u.Email == email && !u.IsDeleted, cancellationToken);
        }
    }
}
