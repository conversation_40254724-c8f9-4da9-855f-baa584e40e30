namespace LocalMailing.Domain.Enums
{
    public enum ConfidentialityLevel
    {
        Internal,
        Restricted,
        Secret
    }

    public enum MailDirection
    {
        Inbound,
        Outbound
    }

    public enum MailMode
    {
        Public,
        Secure
    }

    public enum ParticipantRole
    {
        Sender,
        Receiver
    }

    public enum ParticipantType
    {
        User,
        Department,
        ExternalParty
    }

    public enum Priority
    {
        Normal,
        High,
        Urgent
    }

    public enum WorkflowState
    {
        Draft,
        Registered,
        InReview,
        Routed,
        Completed,
        Closed,
        Archived
    }

}