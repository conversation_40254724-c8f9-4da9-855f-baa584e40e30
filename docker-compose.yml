version: '3.9'

services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: localmailing-db
    restart: always
    environment:
      MYSQL_DATABASE: localmailing
      MYSQL_USER: localmailing
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: rootpassword
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - localmailing-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # API Backend
  api:
    build:
      context: ./src/back-end
      dockerfile: LocalMailing.API/Dockerfile
    container_name: localmailing-api
    restart: always
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=db;Database=localmailing;User=localmailing;Password=password;
      - JwtSettings__SecretKey=YourSuperSecretKeyThatIsAtLeast32CharactersLong!
      - JwtSettings__Issuer=LocalMailingSystem
      - JwtSettings__Audience=LocalMailingSystemUsers
      - JwtSettings__ExpirationInMinutes=60
    depends_on:
      db:
        condition: service_healthy
    networks:
      - localmailing-network
    volumes:
      - api_logs:/app/logs

  # Frontend (React) - Placeholder for future implementation
  frontend:
    image: nginx:alpine
    container_name: localmailing-frontend
    restart: always
    ports:
      - "3000:80"
    volumes:
      - ./src/front-end/build:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    networks:
      - localmailing-network

  # phpMyAdmin for database management (optional)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: localmailing-phpmyadmin
    restart: always
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: localmailing
      PMA_PASSWORD: password
    depends_on:
      - db
    networks:
      - localmailing-network

volumes:
  db_data:
    driver: local
  api_logs:
    driver: local

networks:
  localmailing-network:
    driver: bridge
