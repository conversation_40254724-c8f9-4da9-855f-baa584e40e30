using LocalMailing.Application.DTOs;
using LocalMailing.Domain.Enums;
using MediatR;

namespace LocalMailing.Application.Commands.Messages
{
    public class CreateMessageCommand : IRequest<MessageDto>
    {
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public MailMode Mode { get; set; }
        public MailDirection Direction { get; set; }
        public Priority Priority { get; set; }
        public ConfidentialityLevel ConfidentialityLevel { get; set; }
        public DateTime? DueDate { get; set; }
        public List<Guid> RecipientDepartmentIds { get; set; } = new();
        public Guid CreatedBy { get; set; }
    }
}
