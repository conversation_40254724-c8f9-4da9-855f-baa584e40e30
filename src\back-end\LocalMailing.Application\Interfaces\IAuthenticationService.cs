namespace LocalMailing.Application.Interfaces
{
    public interface IAuthenticationService
    {
        Task<string> AuthenticateAsync(string username, string password, CancellationToken cancellationToken = default);
        Task<bool> ValidateTokenAsync(string token, CancellationToken cancellationToken = default);
        Task<Guid?> GetUserIdFromTokenAsync(string token, CancellationToken cancellationToken = default);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
    }
}
