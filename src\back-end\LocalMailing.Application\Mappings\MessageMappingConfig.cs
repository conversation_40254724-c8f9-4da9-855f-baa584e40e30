using LocalMailing.Application.DTOs;
using LocalMailing.Domain.Entities.Messaging;
using Mapster;

namespace LocalMailing.Application.Mappings
{
    public class MessageMappingConfig : IRegister
    {
        public void Register(TypeAdapterConfig config)
        {
            config.NewConfig<Message, MessageDto>()
                .Map(dest => dest.CreatedByName, src => src.CreatedBy.ToString()) // TODO: Map from User entity
                .Map(dest => dest.Attachments, src => src.Attachments)
                .Map(dest => dest.MessageInfo, src => src.MessageInfo);

            config.NewConfig<MessageAttachment, MessageAttachmentDto>();

            config.NewConfig<MessageInfo, MessageInfoDto>();

            config.NewConfig<CreateMessageDto, CreateMessageCommand>();

            config.NewConfig<UpdateMessageDto, UpdateMessageCommand>();
        }
    }
}
