using LocalMailing.Application.DTOs;
using LocalMailing.Domain.Enums;
using MediatR;

namespace LocalMailing.Application.Commands.Messages
{
    public class UpdateMessageCommand : IRequest<MessageDto>
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Priority Priority { get; set; }
        public DateTime? DueDate { get; set; }
        public Guid UpdatedBy { get; set; }
    }
}
