using LocalMailing.Application.DTOs;
using LocalMailing.Domain.Enums;
using MediatR;

namespace LocalMailing.Application.Queries.Messages
{
    public class GetMessagesQuery : IRequest<List<MessageDto>>
    {
        public Guid UserId { get; set; }
        public Guid? DepartmentId { get; set; }
        public WorkflowState? WorkflowState { get; set; }
        public string? SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;

        public GetMessagesQuery(Guid userId)
        {
            UserId = userId;
        }
    }
}
