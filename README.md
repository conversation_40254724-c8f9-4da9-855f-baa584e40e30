# Local-Mailing System

An enterprise-level internal communication and document management solution built with Clean Architecture principles.

## 🏛️ Architecture

The system follows Clean Architecture with clear separation of concerns:

- **Domain Layer**: Core business entities, value objects, and domain events
- **Application Layer**: CQRS with MediatR, DTOs, validators, and business logic
- **Infrastructure Layer**: EF Core, repositories, and external service implementations
- **API Layer**: ASP.NET Core Web API with JWT authentication and Swagger documentation

## 🛠️ Technology Stack

- **Backend**: ASP.NET Core 9, C#
- **Database**: MySQL 8.0
- **ORM**: Entity Framework Core
- **Architecture**: CQRS + Mediator Pattern (MediatR)
- **Mapping**: Mapster
- **Validation**: FluentValidation
- **Authentication**: JWT Bearer tokens
- **Logging**: Serilog
- **Containerization**: Docker & Docker Compose

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- .NET 9 SDK (for development)
- MySQL 8.0 (if running without Dock<PERSON>)

### Running with Dock<PERSON> (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd local-mailing-v1
   ```

2. **Start the application**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - API: http://localhost:5000
   - Swagger UI: http://localhost:5000
   - phpMyAdmin: http://localhost:8080
   - Frontend: http://localhost:3000 (when implemented)

### Development Setup

1. **Start only the database**
   ```bash
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **Run the API locally**
   ```bash
   cd src/back-end
   dotnet restore
   dotnet run --project LocalMailing.API
   ```

## 📊 Database

The system uses MySQL with the following main entities:

### Security & Access
- Users, Roles, Permissions
- Departments (hierarchical)
- User-Role assignments

### Messaging
- Messages with workflow states
- Message attachments and participants
- Message access tracking

### System Settings
- Document settings and templates

## 🔐 Authentication

The system uses JWT Bearer token authentication:

1. **Login**: POST `/api/auth/login`
   ```json
   {
     "username": "admin",
     "password": "admin123"
   }
   ```

2. **Use the token**: Include in Authorization header
   ```
   Authorization: Bearer <your-jwt-token>
   ```

## 📝 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/validate` - Token validation

### Messages
- `GET /api/messages` - Get messages (with filtering)
- `GET /api/messages/{id}` - Get message by ID
- `POST /api/messages` - Create new message
- `PUT /api/messages/{id}` - Update message
- `DELETE /api/messages/{id}` - Delete message

### Users
- `GET /api/users` - Get users list
- `POST /api/users` - Create new user

## 🔧 Configuration

### Environment Variables

- `ConnectionStrings__DefaultConnection`: Database connection string
- `JwtSettings__SecretKey`: JWT signing key
- `JwtSettings__Issuer`: JWT issuer
- `JwtSettings__Audience`: JWT audience
- `JwtSettings__ExpirationInMinutes`: Token expiration time

### Default Credentials

- **Username**: admin
- **Password**: admin123

⚠️ **Important**: Change the default admin password in production!

## 🏗️ Development

### Project Structure

```
src/
├── back-end/
│   ├── LocalMailing.Domain/          # Domain entities and business rules
│   ├── LocalMailing.Application/     # CQRS, DTOs, and business logic
│   ├── LocalMailing.Infrastructure/  # Data access and external services
│   ├── LocalMailing.API/            # Web API controllers and configuration
│   └── Fly.Framework/               # Custom framework components
└── front-end/                       # React frontend (to be implemented)
```

### Running Tests

```bash
cd src/back-end
dotnet test
```

### Database Migrations

```bash
cd src/back-end/LocalMailing.Infrastructure
dotnet ef migrations add <MigrationName> --startup-project ../LocalMailing.API
dotnet ef database update --startup-project ../LocalMailing.API
```

## 🐳 Docker Commands

```bash
# Start all services
docker-compose up -d

# Start only database (for development)
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f api

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up -d --build
```

## 📋 TODO

- [ ] Complete frontend React application
- [ ] Implement file upload for message attachments
- [ ] Add email notifications
- [ ] Implement advanced search functionality
- [ ] Add audit logging
- [ ] Implement message workflow automation
- [ ] Add unit and integration tests
- [ ] Performance optimization and caching

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
