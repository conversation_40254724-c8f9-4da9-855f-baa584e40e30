namespace LocalMailing.Domain.Exceptions
{
    public abstract class DomainException : Exception
    {
        protected DomainException(string message) : base(message) { }
        protected DomainException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class MessageNotFoundException : DomainException
    {
        public MessageNotFoundException(Guid messageId) 
            : base($"Message with ID {messageId} was not found.") { }
    }

    public class UnauthorizedMessageAccessException : DomainException
    {
        public UnauthorizedMessageAccessException(Guid userId, Guid messageId) 
            : base($"User {userId} is not authorized to access message {messageId}.") { }
    }

    public class InvalidMessageStateTransitionException : DomainException
    {
        public InvalidMessageStateTransitionException(string currentState, string targetState) 
            : base($"Cannot transition message from {currentState} to {targetState}.") { }
    }

    public class DuplicateUserException : DomainException
    {
        public DuplicateUserException(string username) 
            : base($"User with username '{username}' already exists.") { }
    }
}
