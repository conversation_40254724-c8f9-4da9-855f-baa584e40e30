using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Fly.Framework.Domain.Common;

namespace Fly.Framework.Core.Repositories.Persistence;

public class EntityTypeBaseConfiguration<T> : IEntityTypeConfiguration<T>
    where T : EntityBase
{
    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        builder.HasKey(ag => ag.Id);
        builder.Property(ag => ag.Id).HasColumnName("id");

        builder.Property(ag => ag.CreatedAtUtc)
           .HasColumnName("created_at_utc")
           .IsRequired();

        builder.Property(ag => ag.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(ag => ag.UpdatedAtUtc)
            .HasColumnName("updated_at_utc")
            .IsRequired(false);

        builder.Property(ag => ag.UpdatedBy)
            .HasColumnName("updated_by")
            .IsRequired(false);
            
        builder.Property(ag => ag.IsDeleted)
            .HasColumnName("is_deleted")
            .IsRequired();
    }
}