using LocalMailing.Application.DTOs;
using MediatR;

namespace LocalMailing.Application.Commands.Users
{
    public class CreateUserCommand : IRequest<UserDto>
    {
        public string Username { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public Guid DepartmentId { get; set; }
        public Guid CreatedBy { get; set; }
    }
}
