using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LocalMailing.Domain.Events;

public class ParticipantAddedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid ParticipantId { get; }
    public ParticipantRole Role { get; }
    public ParticipantType Type { get; }

    public ParticipantAddedEvent(Guid mailItemId, Guid participantId, ParticipantRole role, ParticipantType type)
    {
        MailItemId = mailItemId;
        ParticipantId = participantId;
        Role = role;
        Type = type;
    }
}

public class ParticipantRemovedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid ParticipantId { get; }

    public ParticipantRemovedEvent(Guid mailItemId, Guid participantId)
    {
        MailItemId = mailItemId;
        ParticipantId = participantId;
    }
}
