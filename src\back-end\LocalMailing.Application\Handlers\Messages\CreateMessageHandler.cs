using LocalMailing.Application.Commands.Messages;
using LocalMailing.Application.DTOs;
using LocalMailing.Application.Repositories;
using LocalMailing.Domain.Entities.Messaging;
using LocalMailing.Domain.Entities.SystemBased;
using LocalMailing.Domain.Enums;
using LocalMailing.Domain.Events;
using Mapster;
using MediatR;

namespace LocalMailing.Application.Handlers.Messages
{
    public class CreateMessageHandler : IRequestHandler<CreateMessageCommand, MessageDto>
    {
        private readonly IMessageRepository _messageRepository;
        private readonly IDepartmentRepository _departmentRepository;

        public CreateMessageHandler(
            IMessageRepository messageRepository,
            IDepartmentRepository departmentRepository)
        {
            _messageRepository = messageRepository;
            _departmentRepository = departmentRepository;
        }

        public async Task<MessageDto> Handle(CreateMessageCommand request, CancellationToken cancellationToken)
        {
            var message = new Message
            {
                Id = Guid.NewGuid(),
                Type = request.Type,
                Title = request.Title,
                Subject = request.Subject,
                Content = request.Content,
                Mode = request.Mode,
                Direction = request.Direction,
                Priority = request.Priority,
                ConfidentialityLevel = request.ConfidentialityLevel,
                WorkflowState = WorkflowState.Draft,
                DueDate = request.DueDate,
                CreatedAtUtc = DateTime.UtcNow,
                CreatedBy = request.CreatedBy
            };

            // Add message parts for recipient departments
            foreach (var departmentId in request.RecipientDepartmentIds)
            {
                message.MessageParts.Add(new MessagePart
                {
                    Id = Guid.NewGuid(),
                    MessageId = message.Id,
                    DepartmentId = departmentId,
                    Role = ParticipantRole.Receiver,
                    Type = ParticipantType.Department,
                    CreatedAtUtc = DateTime.UtcNow,
                    CreatedBy = request.CreatedBy
                });
            }

            var createdMessage = await _messageRepository.AddAsync(message, cancellationToken);
            
            // TODO: Publish domain event
            // await _mediator.Publish(new MessageCreatedEvent(createdMessage.Id, createdMessage.Title, request.CreatedBy), cancellationToken);

            return createdMessage.Adapt<MessageDto>();
        }
    }
}
