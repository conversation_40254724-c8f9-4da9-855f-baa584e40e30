using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Events
{
    public class UserAccessedMessageEvent : BaseDomainEvent
    {
        public Guid MessageId { get; }
        public Guid UserId { get; }
        public string Action { get; }

        public UserAccessedMessageEvent(Guid messageId, Guid userId, string action)
        {
            MessageId = messageId;
            UserId = userId;
            Action = action;
        }
    }
}
