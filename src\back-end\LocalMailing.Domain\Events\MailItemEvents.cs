using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LocalMailing.Domain.Events;

public class MailItemCreatedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public MailDirection Direction { get; }
    public MailMode Mode { get; }

    public MailItemCreatedEvent(Guid mailItemId, MailDirection direction, MailMode mode)
    {
        MailItemId = mailItemId;
        Direction = direction;
        Mode = mode;
    }
}

public class MailItemUpdatedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemUpdatedEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

public class MailItemRegisteredEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemRegisteredEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

public class MailItemMovedToReviewEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemMovedToReviewEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

public class MailItemRoutedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }
    public Guid? FromParticipantId { get; }
    public Guid ToParticipantId { get; }
    public string Action { get; }

    public MailItemRoutedEvent(Guid mailItemId, Guid? fromParticipantId, Guid toParticipantId, string action)
    {
        MailItemId = mailItemId;
        FromParticipantId = fromParticipantId;
        ToParticipantId = toParticipantId;
        Action = action;
    }
}

public class MailItemCompletedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemCompletedEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

public class MailItemClosedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemClosedEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

public class MailItemArchivedEvent : BaseDomainEvent
{
    public Guid MailItemId { get; }

    public MailItemArchivedEvent(Guid mailItemId)
    {
        MailItemId = mailItemId;
    }
}

