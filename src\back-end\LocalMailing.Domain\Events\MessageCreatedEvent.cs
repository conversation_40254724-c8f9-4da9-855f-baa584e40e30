using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Events
{
    public class MessageCreatedEvent : BaseDomainEvent
    {
        public Guid MessageId { get; }
        public string Title { get; }
        public Guid CreatedBy { get; }

        public MessageCreatedEvent(Guid messageId, string title, Guid createdBy)
        {
            MessageId = messageId;
            Title = title;
            CreatedBy = createdBy;
        }
    }
}
