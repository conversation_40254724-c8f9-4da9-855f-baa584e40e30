-- Local Mailing System Database Initialization Script
-- This script creates the initial database structure and seed data

USE localmailing;

-- Create initial departments
INSERT INTO departments (id, name, parent_id, created_at_utc, created_by, is_deleted) VALUES
(UUID(), 'Administration', NULL, NOW(), UUID(), FALSE),
(UUID(), 'Human Resources', NULL, NOW(), UUID(), FALSE),
(UUID(), 'Information Technology', NULL, NOW(), UUID(), FALSE),
(UUID(), 'Finance', NULL, NOW(), UUID(), FALSE),
(UUID(), 'Operations', NULL, NOW(), UUID(), FALSE);

-- Create initial roles
INSERT INTO roles (id, name, description, created_at_utc, created_by, is_deleted) VALUES
(UUID(), 'Administrator', 'System administrator with full access', NOW(), UUID(), FALSE),
(UUID(), 'Manager', 'Department manager with management privileges', NO<PERSON>(), UUID(), FALSE),
(UUID(), 'User', 'Regular user with basic access', NOW(), UUID(), FALSE),
(UUID(), 'Viewer', 'Read-only access to messages', NOW(), UUID(), FALSE);

-- Create initial permissions
INSERT INTO permissions (id, `key`, name, description, created_at_utc, created_by, is_deleted) VALUES
(UUID(), 'message.create', 'Create Message', 'Can create new messages', NOW(), UUID(), FALSE),
(UUID(), 'message.read', 'Read Message', 'Can read messages', NOW(), UUID(), FALSE),
(UUID(), 'message.update', 'Update Message', 'Can update messages', NOW(), UUID(), FALSE),
(UUID(), 'message.delete', 'Delete Message', 'Can delete messages', NOW(), UUID(), FALSE),
(UUID(), 'user.create', 'Create User', 'Can create new users', NOW(), UUID(), FALSE),
(UUID(), 'user.read', 'Read User', 'Can read user information', NOW(), UUID(), FALSE),
(UUID(), 'user.update', 'Update User', 'Can update user information', NOW(), UUID(), FALSE),
(UUID(), 'user.delete', 'Delete User', 'Can delete users', NOW(), UUID(), FALSE),
(UUID(), 'department.manage', 'Manage Department', 'Can manage department settings', NOW(), UUID(), FALSE),
(UUID(), 'system.admin', 'System Administration', 'Full system administration access', NOW(), UUID(), FALSE);

-- Create default admin user (password: admin123)
-- Note: In production, this should be changed immediately
SET @admin_dept_id = (SELECT id FROM departments WHERE name = 'Administration' LIMIT 1);
SET @admin_user_id = UUID();

INSERT INTO users (id, username, phone, email, password_hash, full_name, department_id, created_at_utc, created_by, is_deleted) VALUES
(@admin_user_id, 'admin', '+1234567890', '<EMAIL>', '$2a$11$rQZrXwOJhKKhKKhKKhKKhOeJ8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K', 'System Administrator', @admin_dept_id, NOW(), @admin_user_id, FALSE);

-- Assign admin role to admin user
SET @admin_role_id = (SELECT id FROM roles WHERE name = 'Administrator' LIMIT 1);
INSERT INTO user_roles (id, user_id, role_id, created_at_utc, created_by, is_deleted) VALUES
(UUID(), @admin_user_id, @admin_role_id, NOW(), @admin_user_id, FALSE);

-- Assign all permissions to admin role
INSERT INTO role_permissions (id, role_id, permission_id, created_at_utc, created_by, is_deleted)
SELECT UUID(), @admin_role_id, id, NOW(), @admin_user_id, FALSE
FROM permissions;

-- Create system document settings
INSERT INTO system_document_settings (id, default_page_size, fonts, created_at_utc, created_by, is_deleted) VALUES
(UUID(), 20, 'Arial, Helvetica, sans-serif', NOW(), @admin_user_id, FALSE);

-- Create default document template
INSERT INTO system_document_templates (id, name, background_image, template_content, is_default, created_at_utc, created_by, is_deleted) VALUES
(UUID(), 'Default Template', '', '<html><body>{{content}}</body></html>', TRUE, NOW(), @admin_user_id, FALSE);

COMMIT;
