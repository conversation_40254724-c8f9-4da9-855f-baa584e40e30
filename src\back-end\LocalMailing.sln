
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocalMailing.Infrastructure", "LocalMailing.Infrastructure\LocalMailing.Infrastructure.csproj", "{4209EEE3-2169-4F5F-838B-F9E62F1F663F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocalMailing.Domain", "LocalMailing.Domain\LocalMailing.Domain.csproj", "{7C727BFE-302A-4FF0-9417-0044090A753B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocalMailing.Application", "LocalMailing.Application\LocalMailing.Application.csproj", "{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Fly.Framework", "Fly.Framework", "{7F5413D6-63A9-6E9C-02E0-E92031711A8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fly.Framework.Core", "Fly.Framework\Fly.Framework.Core\Fly.Framework.Core.csproj", "{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Fly.Framework.Domain", "Fly.Framework\Fly.Framework.Domain\Fly.Framework.Domain.csproj", "{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LocalMailing.API", "LocalMailing.API\LocalMailing.API.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|x64.Build.0 = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Debug|x86.Build.0 = Debug|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|Any CPU.Build.0 = Release|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|x64.ActiveCfg = Release|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|x64.Build.0 = Release|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|x86.ActiveCfg = Release|Any CPU
		{4209EEE3-2169-4F5F-838B-F9E62F1F663F}.Release|x86.Build.0 = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|x64.Build.0 = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Debug|x86.Build.0 = Debug|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|x64.ActiveCfg = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|x64.Build.0 = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|x86.ActiveCfg = Release|Any CPU
		{7C727BFE-302A-4FF0-9417-0044090A753B}.Release|x86.Build.0 = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|x64.Build.0 = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Debug|x86.Build.0 = Debug|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|x64.ActiveCfg = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|x64.Build.0 = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|x86.ActiveCfg = Release|Any CPU
		{E082B5EB-384D-41F2-9922-7F8EAE3A05BC}.Release|x86.Build.0 = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|x64.Build.0 = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Debug|x86.Build.0 = Debug|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|x64.ActiveCfg = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|x64.Build.0 = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|x86.ActiveCfg = Release|Any CPU
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13}.Release|x86.Build.0 = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|x64.Build.0 = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Debug|x86.Build.0 = Debug|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|Any CPU.Build.0 = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|x64.ActiveCfg = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|x64.Build.0 = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|x86.ActiveCfg = Release|Any CPU
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A}.Release|x86.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x64.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|x86.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x64.Build.0 = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9F4D4EC1-6D4F-4A47-8BDF-E0ECB099FF13} = {7F5413D6-63A9-6E9C-02E0-E92031711A8D}
		{774453E3-B6E1-4CF1-9DE8-E4BCE5D7B15A} = {7F5413D6-63A9-6E9C-02E0-E92031711A8D}
	EndGlobalSection
EndGlobal
