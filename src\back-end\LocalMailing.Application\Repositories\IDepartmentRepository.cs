using LocalMailing.Domain.Entities.SystemBased;

namespace LocalMailing.Application.Repositories
{
    public interface IDepartmentRepository : IRepository<Department>
    {
        Task<List<Department>> GetHierarchyAsync(CancellationToken cancellationToken = default);
        Task<List<Department>> GetChildrenAsync(Guid parentId, CancellationToken cancellationToken = default);
        Task<Department?> GetWithUsersAsync(Guid id, CancellationToken cancellationToken = default);
        Task<bool> HasChildrenAsync(Guid id, CancellationToken cancellationToken = default);
    }
}
