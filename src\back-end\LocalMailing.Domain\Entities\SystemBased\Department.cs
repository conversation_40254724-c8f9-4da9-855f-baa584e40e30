using Fly.Framework.Domain.Common;
using LocalMailing.Domain.Entities.Messaging;

namespace LocalMailing.Domain.Entities.SystemBased
{
    public class Department : EntityBase
    {
        public string Name { get; set; } = string.Empty;
        public Guid? ParentId { get; set; }

        // Navigation properties
        public virtual Department? Parent { get; set; }
        public virtual ICollection<Department> Children { get; set; } = new List<Department>();
        public virtual ICollection<TUser> Users { get; set; } = new List<TUser>();
        public virtual ICollection<MessagePart> MessageParts { get; set; } = new List<MessagePart>();
    }
}
