using Fly.Framework.Domain.Common;

namespace LocalMailing.Domain.Entities.SystemBased
{
    public class Permission : EntityBase
    {
        public string Key { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
}
