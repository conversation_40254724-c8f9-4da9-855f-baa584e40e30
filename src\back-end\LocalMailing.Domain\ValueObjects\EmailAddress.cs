using System.Text.RegularExpressions;

namespace LocalMailing.Domain.ValueObjects
{
    public record EmailAddress
    {
        private static readonly Regex EmailRegex = new(
            @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public string Value { get; }

        public EmailAddress(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Email address cannot be empty", nameof(value));

            if (!EmailRegex.IsMatch(value))
                throw new ArgumentException("Invalid email address format", nameof(value));

            Value = value.Trim().ToLowerInvariant();
        }

        public static implicit operator string(EmailAddress email) => email.Value;
        public static implicit operator EmailAddress(string value) => new(value);

        public override string ToString() => Value;
    }
}
